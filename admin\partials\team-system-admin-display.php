<div class="wrap">
    <h1 class="wp-heading-inline">إدارة الفرق</h1>
    <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="page-title-action">إضافة فريق جديد</a>
    <hr class="wp-header-end">
    
    <div class="team-list">
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>الشعار</th>
                    <th>اسم الفريق</th>
                    <th>الأعضاء</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                global $wpdb;
                $teams = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}teams ORDER BY created_at DESC");
                
                foreach ($teams as $team) {
                    $member_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}team_members WHERE team_id = %d",
                        $team->id
                    ));
                    ?>
                    <tr>
                        <td>
                            <?php if ($team->logo_url): ?>
                                <img src="<?php echo esc_url($team->logo_url); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">
                            <?php else: ?>
                                <div style="width: 50px; height: 50px; background: #f0f0f1; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                                    <span class="dashicons dashicons-groups" style="font-size: 24px; width: 24px; height: 24px; color: #8c8f94;"></span>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo esc_html($team->name); ?></strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo admin_url('admin.php?page=team-system&action=edit&team_id=' . $team->id); ?>">تعديل</a> |
                                </span>
                                <span class="view">
                                    <a href="<?php echo home_url('/teams/' . $team->slug); ?>" target="_blank">عرض</a>
                                </span>
                            </div>
                        </td>
                        <td><?php echo intval($member_count); ?> عضو</td>
                        <td><?php echo date_i18n('j F Y', strtotime($team->created_at)); ?></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=team-system&action=edit&team_id=' . $team->id); ?>" class="button button-primary">تعديل</a>
                            <a href="#" class="button delete-team" data-team-id="<?php echo $team->id; ?>">حذف</a>
                        </td>
                    </tr>
                    <?php
                }
                
                if (empty($teams)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px;">
                            لا توجد فرق حتى الآن. <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>">أنشئ فريقك الأول</a>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle team deletion
    $('.delete-team').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من حذف هذا الفريق؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }
        
        var $button = $(this);
        var teamId = $button.data('team-id');
        
        $.post(ajaxurl, {
            action: 'team_system_delete_team',
            team_id: teamId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $button.closest('tr').fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data.message || 'حدث خطأ أثناء حذف الفريق');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });
});
</script>