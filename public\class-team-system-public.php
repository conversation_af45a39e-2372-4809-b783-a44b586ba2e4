<?php
class Team_System_Public {
    private $plugin_name;
    private $version;

    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        add_shortcode('team_profile', array($this, 'render_team_profile'));
        add_shortcode('team_listing', array($this, 'render_team_listing'));
        add_action('init', array($this, 'register_rewrite_rules'));
        add_filter('query_vars', array($this, 'register_query_vars'));
        add_action('template_redirect', array($this, 'team_template_redirect'));

        // Add team info to chapter pages
        add_filter('the_content', array($this, 'add_team_info_to_chapter'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_team_styles'));
    }

    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'css/team-system-public.css',
            array(),
            $this->version,
            'all'
        );
    }

    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'js/team-system-public.js',
            array('jquery'),
            $this->version,
            false
        );
    }

    public function register_rewrite_rules() {
        // Use the register_rewrite_rules method from Team_System_Activator
        Team_System_Activator::register_rewrite_rules();
    }

    public function register_query_vars($vars) {
        // Add team_slug to query vars
        $vars[] = 'team_slug';
        return $vars;
    }

    public function team_template_redirect() {
        global $wp_query, $wpdb;
        
        if (isset($wp_query->query_vars['team_slug'])) {
            $team_slug = $wp_query->query_vars['team_slug'];
            
            // Get team by slug
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
                $team_slug
            ));
            
            if ($team) {
                // Set up the query vars for our template
                $wp_query->is_404 = false;
                $wp_query->is_single = true;
                $wp_query->is_singular = true;
                status_header(200);
                
                // Include our template
                $template = locate_template('single-team.php');
                if (!empty($template)) {
                    load_template($template);
                } else {
                    // Fallback to plugin template
                    $this->render_team_profile($team_slug);
                }
                exit();
            } else {
                // Team not found
                $wp_query->set_404();
                status_header(404);
                get_template_part('404');
                exit();
            }
        }
    }

    public function render_team_profile($atts) {
        global $wpdb;
        
        $atts = shortcode_atts(array(
            'id' => 0,
            'slug' => ''
        ), $atts, 'team_profile');
        
        // Get team by ID or slug
        if (!empty($atts['id'])) {
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
                intval($atts['id'])
            ));
        } elseif (!empty($atts['slug'])) {
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
                sanitize_title($atts['slug'])
            ));
        } else {
            return '<p>الرجاء توفير معرف الفريق أو الرابط المختصر.</p>';
        }
        
        if (!$team) {
            return '<p>لم يتم العثور على الفريق المطلوب.</p>';
        }
        
        // Get team members
        $members = $wpdb->get_results($wpdb->prepare(
            "SELECT u.ID, u.display_name, u.user_email, tm.role, tm.joined_at 
             FROM {$wpdb->prefix}team_members tm
             INNER JOIN {$wpdb->users} u ON tm.user_id = u.ID
             WHERE tm.team_id = %d
             ORDER BY tm.role = 'leader' DESC, u.display_name",
            $team->id
        ));
        
        // Get role names
        $team_system = new Team_System_Admin('team-system', '1.0.0');
        
        // Start output buffering
        ob_start();
        
        // Include template file
        $template_path = plugin_dir_path(__FILE__) . 'partials/team-profile.php';
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            echo '<p>عذرًا، تعذر تحميل قالب عرض الفريق.</p>';
        }
        
        // Return the buffered content
        return ob_get_clean();
    }

    public function render_team_listing($atts) {
        $atts = shortcode_atts(array(
            'limit' => 10,
            'orderby' => 'name',
            'order' => 'ASC'
        ), $atts);

        ob_start();
        include plugin_dir_path(__FILE__) . 'partials/team-listing.php';
        return ob_get_clean();
    }

    /**
     * Add team information to chapter content
     */
    public function add_team_info_to_chapter($content) {
        // Only for single chapter pages
        if (!is_single() || get_post_type() !== 'chapter') {
            return $content;
        }

        global $post;

        // Check if this chapter was published as a team
        $publish_as_team = get_post_meta($post->ID, '_publish_as_team', true);
        if ($publish_as_team !== '1') {
            return $content;
        }

        $team_id = get_post_meta($post->ID, '_team_id', true);
        $team_members_roles = get_post_meta($post->ID, '_team_members_roles', true);

        if (!$team_id) {
            return $content;
        }

        // Get team information
        global $wpdb;
        $team = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
            $team_id
        ));

        if (!$team) {
            return $content;
        }

        // Build team info HTML
        $team_info = $this->build_team_info_html($team, $team_members_roles);

        // Add team info after content
        return $content . $team_info;
    }

    /**
     * Build team information HTML
     */
    private function build_team_info_html($team, $team_members_roles) {
        $html = '<div class="chapter-team-info">';
        $html .= '<h3 class="team-info-title">طاقم العمل</h3>';

        // Team name and logo
        $html .= '<div class="team-header">';
        if (!empty($team->logo_url)) {
            $html .= '<img src="' . esc_url($team->logo_url) . '" alt="' . esc_attr($team->name) . '" class="team-logo">';
        }
        $html .= '<div class="team-details">';
        $html .= '<h4 class="team-name"><a href="/team/' . esc_attr($team->slug) . '">' . esc_html($team->name) . '</a></h4>';
        if (!empty($team->description)) {
            $html .= '<p class="team-description">' . esc_html(wp_trim_words($team->description, 20)) . '</p>';
        }
        $html .= '</div>';
        $html .= '</div>';

        // Team members who worked on this chapter
        if (!empty($team_members_roles)) {
            $html .= '<div class="chapter-contributors">';
            $html .= '<h5>المساهمون في هذا الفصل:</h5>';
            $html .= '<div class="contributors-list">';

            foreach ($team_members_roles as $user_id => $role) {
                if (empty($role)) continue;

                $user = get_userdata($user_id);
                if (!$user) continue;

                $role_name = $this->get_role_display_name($role);
                $avatar = get_avatar($user_id, 40);

                $html .= '<div class="contributor-item">';
                $html .= '<div class="contributor-avatar">' . $avatar . '</div>';
                $html .= '<div class="contributor-info">';
                $html .= '<span class="contributor-name"><a href="' . get_author_posts_url($user_id) . '">' . esc_html($user->display_name) . '</a></span>';
                $html .= '<span class="contributor-role">' . esc_html($role_name) . '</span>';
                $html .= '</div>';
                $html .= '</div>';
            }

            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Get role display name
     */
    private function get_role_display_name($role) {
        $roles = array(
            'translator' => 'مترجم',
            'editor' => 'مدقق لغوي',
            'designer' => 'مصمم',
            'reviewer' => 'مراجع جودة'
        );

        return isset($roles[$role]) ? $roles[$role] : $role;
    }

    /**
     * Enqueue team styles for frontend
     */
    public function enqueue_team_styles() {
        if (is_single() && get_post_type() === 'chapter') {
            wp_add_inline_style('wp-block-library', $this->get_team_info_css());
        }
    }

    /**
     * Get CSS for team info display
     */
    private function get_team_info_css() {
        return '
        .chapter-team-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            font-family: "Cairo", sans-serif;
        }

        .team-info-title {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .team-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .team-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-left: 15px;
            object-fit: cover;
            border: 3px solid #3498db;
        }

        .team-details {
            flex: 1;
        }

        .team-name {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .team-name a {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .team-name a:hover {
            color: #3498db;
        }

        .team-description {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .chapter-contributors h5 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .contributors-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .contributor-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .contributor-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .contributor-avatar {
            margin-left: 12px;
        }

        .contributor-avatar img {
            border-radius: 50%;
            border: 2px solid #e9ecef;
        }

        .contributor-info {
            display: flex;
            flex-direction: column;
        }

        .contributor-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .contributor-name a {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contributor-name a:hover {
            color: #3498db;
        }

        .contributor-role {
            font-size: 12px;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        @media (max-width: 768px) {
            .team-header {
                flex-direction: column;
                text-align: center;
            }

            .team-logo {
                margin: 0 0 10px 0;
            }

            .contributors-list {
                grid-template-columns: 1fr;
            }
        }
        ';
    }
}