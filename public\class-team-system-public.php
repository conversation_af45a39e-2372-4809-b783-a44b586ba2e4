<?php
class Team_System_Public {
    private $plugin_name;
    private $version;

    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        add_shortcode('team_profile', array($this, 'render_team_profile'));
        add_shortcode('team_listing', array($this, 'render_team_listing'));
        add_action('init', array($this, 'register_rewrite_rules'));
        add_filter('query_vars', array($this, 'register_query_vars'));
        add_action('template_redirect', array($this, 'team_template_redirect'));
    }

    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'css/team-system-public.css',
            array(),
            $this->version,
            'all'
        );
    }

    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'js/team-system-public.js',
            array('jquery'),
            $this->version,
            false
        );
    }

    public function register_rewrite_rules() {
        // Use the register_rewrite_rules method from Team_System_Activator
        Team_System_Activator::register_rewrite_rules();
    }

    public function register_query_vars($vars) {
        // Add team_slug to query vars
        $vars[] = 'team_slug';
        return $vars;
    }

    public function team_template_redirect() {
        global $wp_query, $wpdb;
        
        if (isset($wp_query->query_vars['team_slug'])) {
            $team_slug = $wp_query->query_vars['team_slug'];
            
            // Get team by slug
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
                $team_slug
            ));
            
            if ($team) {
                // Set up the query vars for our template
                $wp_query->is_404 = false;
                $wp_query->is_single = true;
                $wp_query->is_singular = true;
                status_header(200);
                
                // Include our template
                $template = locate_template('single-team.php');
                if (!empty($template)) {
                    load_template($template);
                } else {
                    // Fallback to plugin template
                    $this->render_team_profile($team_slug);
                }
                exit();
            } else {
                // Team not found
                $wp_query->set_404();
                status_header(404);
                get_template_part('404');
                exit();
            }
        }
    }

    public function render_team_profile($atts) {
        global $wpdb;
        
        $atts = shortcode_atts(array(
            'id' => 0,
            'slug' => ''
        ), $atts, 'team_profile');
        
        // Get team by ID or slug
        if (!empty($atts['id'])) {
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
                intval($atts['id'])
            ));
        } elseif (!empty($atts['slug'])) {
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
                sanitize_title($atts['slug'])
            ));
        } else {
            return '<p>الرجاء توفير معرف الفريق أو الرابط المختصر.</p>';
        }
        
        if (!$team) {
            return '<p>لم يتم العثور على الفريق المطلوب.</p>';
        }
        
        // Get team members
        $members = $wpdb->get_results($wpdb->prepare(
            "SELECT u.ID, u.display_name, u.user_email, tm.role, tm.joined_at 
             FROM {$wpdb->prefix}team_members tm
             INNER JOIN {$wpdb->users} u ON tm.user_id = u.ID
             WHERE tm.team_id = %d
             ORDER BY tm.role = 'leader' DESC, u.display_name",
            $team->id
        ));
        
        // Get role names
        $team_system = new Team_System_Admin('team-system', '1.0.0');
        
        // Start output buffering
        ob_start();
        
        // Include template file
        $template_path = plugin_dir_path(__FILE__) . 'partials/team-profile.php';
        if (file_exists($template_path)) {
            include $template_path;
        } else {
            echo '<p>عذرًا، تعذر تحميل قالب عرض الفريق.</p>';
        }
        
        // Return the buffered content
        return ob_get_clean();
    }

    public function render_team_listing($atts) {
        $atts = shortcode_atts(array(
            'limit' => 10,
            'orderby' => 'name',
            'order' => 'ASC'
        ), $atts);

        ob_start();
        include plugin_dir_path(__FILE__) . 'partials/team-listing.php';
        return ob_get_clean();
    }
}